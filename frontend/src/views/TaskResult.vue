<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack" class="back-btn">
          返回
        </el-button>
        <div class="header-info">
          <h2>{{ task?.name || '抓取结果' }}</h2>
          <div class="result-meta" v-if="result">
            <div class="meta-item">
              <el-icon><DataBoard /></el-icon>
              <span>共抓取 {{ result.totalCount }} 条数据</span>
            </div>
            <div class="meta-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatTime(result.scrapedAt) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="header-actions" v-if="result && result.data.length > 0">
        <el-button :icon="Refresh" @click="refreshResult" class="refresh-btn">
          刷新
        </el-button>
        <el-dropdown @command="handleExport">
          <el-button type="primary" :icon="Download" class="export-btn">
            导出数据 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu class="export-menu">
              <el-dropdown-item command="csv">
                <el-icon><Document /></el-icon>
                <span>导出为 CSV</span>
              </el-dropdown-item>
              <el-dropdown-item command="json">
                <el-icon><Folder /></el-icon>
                <span>导出为 JSON</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 任务信息卡片 -->
    <div v-if="task" class="task-info-section">
      <div class="section-header">
        <div class="section-title">
          <el-icon class="section-icon"><InfoFilled /></el-icon>
          <h3>任务信息</h3>
        </div>
        <el-tag :type="getStatusType(task.status)" size="large" effect="light" class="status-tag">
          <el-icon class="status-icon">
            <Loading v-if="task.status === 'running'" />
            <SuccessFilled v-else-if="task.status === 'success'" />
            <WarningFilled v-else-if="task.status === 'failed'" />
            <Clock v-else />
          </el-icon>
          {{ getStatusText(task.status) }}
        </el-tag>
      </div>
      
      <div class="task-summary modern-card">
        <div class="summary-grid">
          <div class="summary-item">
            <div class="item-header">
              <el-icon class="item-icon"><Link /></el-icon>
              <span class="item-label">目标网址</span>
            </div>
            <el-link :href="task.url" target="_blank" type="primary" class="item-value url-link">
              {{ task.url }}
            </el-link>
          </div>
          
          <div class="summary-item">
            <div class="item-header">
              <el-icon class="item-icon"><Collection /></el-icon>
              <span class="item-label">抓取字段</span>
            </div>
            <div class="fields-tags">
              <el-tag
                v-for="(field, index) in task.fields.slice(0, 5)"
                :key="index"
                size="small"
                effect="plain"
                class="field-tag"
              >
                {{ field.fieldName }}
              </el-tag>
              <el-tag
                v-if="task.fields.length > 5"
                size="small"
                type="info"
                effect="plain"
                class="field-tag more-tag"
              >
                +{{ task.fields.length - 5 }} 个
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="result-card">
      <template #header>
        <div class="card-header">
          <span>抓取结果</span>
          <div class="header-tools" v-if="result && result.data.length > 0">
            <el-input
              v-model="searchText"
              placeholder="搜索数据..."
              size="small"
              style="width: 200px"
              :prefix-icon="Search"
              clearable
            />
            <el-button
              size="small"
              :icon="Filter"
              @click="showColumnSelector = true"
            >
              筛选列
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载结果...">
      </div>

      <div v-else-if="!result" class="empty-container">
        <el-empty description="暂无抓取结果">
          <el-button type="primary" @click="runTask" v-if="task">
            重新执行抓取
          </el-button>
        </el-empty>
      </div>

      <div v-else-if="result.data.length === 0" class="empty-container">
        <el-empty description="抓取结果为空">
          <el-button type="primary" @click="runTask" v-if="task">
            重新执行抓取
          </el-button>
        </el-empty>
      </div>

      <div v-else class="table-container">
        <el-table
          :data="paginatedData"
          class="data-table"
          stripe
          border
          max-height="600"
          empty-text="没有找到匹配的数据"
        >
          <el-table-column
            v-for="column in visibleColumns"
            :key="column.key"
            :prop="column.key"
            :label="column.label"
            :min-width="column.width"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="column.type === 'link' && row[column.key]">
                <el-link
                  :href="row[column.key]"
                  target="_blank"
                  type="primary"
                  style="word-break: break-all;"
                >
                  {{ row[column.key] }}
                </el-link>
              </span>
              <span v-else-if="column.type === 'image' && row[column.key]">
                <el-image
                  :src="row[column.key]"
                  :preview-src-list="[row[column.key]]"
                  style="width: 50px; height: 50px; object-fit: cover;"
                  fit="cover"
                />
              </span>
              <span v-else>{{ row[column.key] || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container" v-if="filteredData.length > pageSize">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredData.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 列筛选对话框 -->
    <el-dialog
      v-model="showColumnSelector"
      title="选择显示的列"
      width="400px"
    >
      <el-checkbox-group v-model="selectedColumns">
        <div v-for="column in allColumns" :key="column.key" style="margin-bottom: 12px;">
          <el-checkbox :label="column.key">{{ column.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      
      <template #footer>
        <el-button @click="showColumnSelector = false">取消</el-button>
        <el-button type="primary" @click="applyColumnFilter">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Download,
  Document,
  Folder,
  Search,
  Filter,
  ArrowDown,
  DataBoard,
  Clock,
  InfoFilled,
  Loading,
  SuccessFilled,
  WarningFilled,
  Link,
  Collection
} from '@element-plus/icons-vue'
import { taskApi } from '@/api'
import type { ScrapingTask, ScrapingResult, TaskStatus } from '@/types'
import dayjs from 'dayjs'
import { saveAs } from 'file-saver'

const router = useRouter()
const route = useRoute()

const task = ref<ScrapingTask>()
const result = ref<ScrapingResult>()
const loading = ref(false)
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const showColumnSelector = ref(false)
const selectedColumns = ref<string[]>([])

interface TableColumn {
  key: string
  label: string
  width: number
  type?: 'text' | 'link' | 'image'
}

const allColumns = ref<TableColumn[]>([])
const visibleColumns = ref<TableColumn[]>([])

// 过滤后的数据
const filteredData = computed(() => {
  if (!result.value || !searchText.value) {
    return result.value?.data || []
  }
  
  const searchLower = searchText.value.toLowerCase()
  return result.value.data.filter(row => {
    return Object.values(row).some(value => 
      String(value).toLowerCase().includes(searchLower)
    )
  })
})

// 分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 获取任务数据
const fetchTask = async () => {
  const taskId = route.params.id as string
  try {
    const response = await taskApi.getTask(taskId)
    if (response.success && response.data) {
      task.value = response.data
    }
  } catch (error) {
    ElMessage.error('获取任务信息失败')
    console.error('获取任务信息失败:', error)
  }
}

// 获取抓取结果
const fetchResult = async () => {
  const taskId = route.params.id as string
  try {
    loading.value = true
    const response = await taskApi.getTaskResult(taskId)
    
    if (response.success && response.data) {
      result.value = response.data
      initializeColumns()
    } else {
      result.value = undefined
    }
  } catch (error) {
    console.error('获取抓取结果失败:', error)
    result.value = undefined
  } finally {
    loading.value = false
  }
}

// 初始化表格列
const initializeColumns = () => {
  if (!result.value || result.value.data.length === 0) return
  
  const sampleRow = result.value.data[0]
  const columns: TableColumn[] = []
  
  Object.keys(sampleRow).forEach(key => {
    const value = sampleRow[key]
    let type: 'text' | 'link' | 'image' = 'text'
    
    // 根据字段名称和值推断类型
    if (key.includes('链接') || key.includes('url') || key.includes('href')) {
      type = 'link'
    } else if (key.includes('图片') || key.includes('img') || key.includes('src')) {
      type = 'image'
    } else if (typeof value === 'string' && (value.startsWith('http') && (value.includes('.jpg') || value.includes('.png') || value.includes('.gif')))) {
      type = 'image'
    } else if (typeof value === 'string' && value.startsWith('http')) {
      type = 'link'
    }
    
    columns.push({
      key,
      label: key,
      width: type === 'image' ? 80 : 150,
      type
    })
  })
  
  allColumns.value = columns
  visibleColumns.value = [...columns]
  selectedColumns.value = columns.map(c => c.key)
}

// 应用列筛选
const applyColumnFilter = () => {
  visibleColumns.value = allColumns.value.filter(col => 
    selectedColumns.value.includes(col.key)
  )
  showColumnSelector.value = false
}

// 刷新结果
const refreshResult = () => {
  fetchResult()
}

// 重新执行任务
const runTask = async () => {
  if (!task.value) return
  
  try {
    const response = await taskApi.runTask(task.value.id)
    if (response.success) {
      ElMessage.success('抓取任务已开始执行')
      task.value.status = 'running'
      
      // 定期检查任务状态
      const checkStatus = async () => {
        const statusResponse = await taskApi.getTask(task.value!.id)
        if (statusResponse.success && statusResponse.data) {
          task.value = statusResponse.data
          
          if (task.value.status === 'success') {
            ElMessage.success('抓取任务执行成功')
            await fetchResult()
          } else if (task.value.status === 'failed') {
            ElMessage.error(`抓取任务执行失败: ${task.value.error || '未知错误'}`)
          } else if (task.value.status === 'running') {
            setTimeout(checkStatus, 2000)
          }
        }
      }
      
      setTimeout(checkStatus, 2000)
    }
  } catch (error) {
    ElMessage.error('启动抓取任务失败')
    console.error('启动抓取任务失败:', error)
  }
}

// 导出数据
const handleExport = (format: string) => {
  if (!result.value || result.value.data.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }
  
  const data = filteredData.value
  const filename = `${task.value?.name || '抓取结果'}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}`
  
  if (format === 'csv') {
    exportToCSV(data, filename)
  } else if (format === 'json') {
    exportToJSON(data, filename)
  }
}

// 导出为CSV
const exportToCSV = (data: any[], filename: string) => {
  if (data.length === 0) return
  
  const headers = visibleColumns.value.map(col => col.label)
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      visibleColumns.value.map(col => {
        const value = row[col.key] || ''
        // 处理包含逗号或引号的值
        return typeof value === 'string' && (value.includes(',') || value.includes('"'))
          ? `"${value.replace(/"/g, '""')}"`
          : value
      }).join(',')
    )
  ].join('\n')
  
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
  saveAs(blob, `${filename}.csv`)
  
  ElMessage.success('CSV文件导出成功')
}

// 导出为JSON
const exportToJSON = (data: any[], filename: string) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' })
  saveAs(blob, `${filename}.json`)
  
  ElMessage.success('JSON文件导出成功')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 获取状态类型
const getStatusType = (status: TaskStatus) => {
  const statusMap = {
    pending: '',
    running: 'info',
    success: 'success',
    failed: 'danger'
  }
  return statusMap[status]
}

// 获取状态文本
const getStatusText = (status: TaskStatus) => {
  const statusMap = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败'
  }
  return statusMap[status]
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 返回
const goBack = () => {
  router.push('/tasks')
}

// 监听搜索文本变化，重置分页
watch(searchText, () => {
  currentPage.value = 1
})

onMounted(async () => {
  await fetchTask()
  await fetchResult()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 0 4px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.back-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--color-bg-primary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

.header-info h2 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.meta-item .el-icon {
  color: var(--color-primary);
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: var(--color-bg-primary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

.export-btn {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  font-weight: 600;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.export-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.export-menu {
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-light);
}

.export-menu .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-weight: 500;
}

/* 任务信息样式 */
.task-info-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.section-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: var(--radius-lg);
}

.status-icon {
  font-size: 16px;
}

.task-summary {
  padding: 32px;
  border-radius: var(--radius-lg);
  background: var(--color-bg-primary);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-icon {
  color: var(--color-primary);
  font-size: 16px;
}

.item-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.url-link {
  word-break: break-all;
  line-height: 1.5;
  font-weight: 500;
}

.fields-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.field-tag {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: 12px;
  padding: 4px 8px;
  border-radius: var(--radius-md);
}

.more-tag {
  background: var(--color-bg-tertiary);
  color: var(--color-text-muted);
}

.task-info-card {
  border-radius: 8px;
}

.task-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.result-card {
  border-radius: 8px;
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-tools {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-container,
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.table-container {
  width: 100%;
}

.data-table {
  width: 100%;
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-left {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .task-summary {
    gap: 8px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .summary-item .label {
    min-width: auto;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-tools {
    width: 100%;
    justify-content: space-between;
  }
  
  .data-table {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .data-table :deep(.el-table__cell) {
    padding: 8px 4px;
  }
  
  .pagination-container {
    padding: 12px 0;
  }
  
  .pagination-container :deep(.el-pagination) {
    font-size: 12px;
  }
}
</style> 