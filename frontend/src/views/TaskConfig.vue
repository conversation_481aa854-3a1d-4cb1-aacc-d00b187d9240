<template>
  <div class="task-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="goBack" :icon="ArrowLeft" size="large" class="back-btn">
            返回任务列表
          </el-button>
        </div>
        <div class="header-center">
          <h1 class="page-title">{{ isEdit ? '编辑抓取任务' : '创建抓取任务' }}</h1>
          <p class="page-subtitle">{{ isEdit ? '修改任务配置和抓取字段' : '配置目标网页和要抓取的数据字段' }}</p>
        </div>
        <div class="header-right">
          <el-button 
            type="primary" 
            @click="saveTask" 
            :loading="saving"
            :icon="Check"
            size="large"
            class="save-btn"
          >
            {{ isEdit ? '更新任务' : '保存任务' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-container">
      <el-steps :active="currentStep" align-center class="config-steps">
        <el-step title="基本信息" description="设置任务名称和目标网址" :icon="Setting" />
        <el-step title="登录配置" description="配置页面登录信息（可选）" :icon="Key" />
        <el-step title="页面预览" description="加载并预览目标页面" :icon="Monitor" />
        <el-step title="智能识别" description="自动识别页面数据结构" :icon="Star" />
        <el-step title="字段配置" description="选择要抓取的数据字段" :icon="Collection" />
        <el-step title="完成配置" description="确认并保存任务配置" :icon="Check" />
      </el-steps>
    </div>

    <div class="config-content">
      <!-- 步骤1: 基本信息 -->
      <div v-show="currentStep === 0" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Setting /></el-icon>
            <h2>基本信息配置</h2>
          </div>
          
          <el-form :model="taskForm" label-width="120px" class="basic-form" size="large">
            <el-form-item label="任务名称" required>
              <el-input
                v-model="taskForm.name"
                placeholder="请输入任务名称，如：新闻列表抓取"
                maxlength="50"
                show-word-limit
                class="form-input"
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>建议使用描述性的名称，方便后续管理</span>
              </div>
            </el-form-item>
            
            <el-form-item label="目标网址" required>
              <div class="url-input-container">
                <el-input
                  v-model="taskForm.url"
                  placeholder="请输入要抓取的网页URL，如：https://example.com"
                  @blur="onUrlChange"
                  class="form-input"
                />
                <el-button
                  type="primary"
                  @click="nextStep"
                  :disabled="!canProceedToNext"
                  size="large"
                  :icon="ArrowRight"
                  class="next-btn"
                >
                  下一步：配置登录
                </el-button>
              </div>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>支持HTTP/HTTPS协议，确保网页可以正常访问</span>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 步骤2: 登录配置 -->
      <div v-show="currentStep === 1" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Key /></el-icon>
            <h2>登录配置</h2>
            <div class="step-actions">
              <div class="action-left">
                <el-button @click="prevStep" :icon="ArrowLeft">返回基本信息</el-button>
              </div>
              <div class="action-right">
                <el-button 
                  v-if="!loginPagePreview"
                  type="primary" 
                  @click="loadLoginPagePreview" 
                  :loading="previewLoading"
                  :icon="Monitor"
                  plain
                >
                  {{ previewLoading ? '正在加载...' : '预览页面' }}
                </el-button>
                <el-button 
                  type="primary" 
                  @click="proceedToNextStep" 
                  :loading="previewLoading"
                  :icon="ArrowRight"
                >
                  {{ previewLoading ? '智能检测中...' : '继续配置字段' }}
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="login-config-layout">
            <!-- 登录配置说明 -->
            <div class="login-config-info">
              <el-alert
                title="登录配置向导"
                type="info"
                :closable="false"
                class="config-guide"
              >
                <template #default>
                  <div class="guide-content">
                    <div class="guide-section">
                      <h4><el-icon><InfoFilled /></el-icon> 页面类型选择</h4>
                      <div class="guide-options">
                        <div class="guide-option">
                          <el-tag type="primary" size="small">首页（需要登录）</el-tag>
                          <span>输入的URL需要登录后才能访问</span>
                        </div>
                        <div class="guide-option">
                          <el-tag type="warning" size="small">登录页面</el-tag>
                          <span>输入的URL是登录页面本身</span>
                        </div>
                        <div class="guide-option">
                          <el-tag type="success" size="small">数据页面（无需登录）</el-tag>
                          <span>可以直接访问，无需登录</span>
                        </div>
                      </div>
                    </div>
                    <div class="guide-section">
                      <h4><el-icon><Key /></el-icon> 登录方式</h4>
                      <div class="guide-options">
                        <div class="guide-option">
                          <el-tag type="primary" size="small">表单登录</el-tag>
                          <span>通过用户名密码表单登录</span>
                        </div>
                        <div class="guide-option">
                          <el-tag type="info" size="small">Cookie登录</el-tag>
                          <span>使用预设Cookie进行身份验证</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
            
            <!-- 左侧：登录配置表单 -->
            <div class="login-form-section">
              <div class="form-section-header">
                <h3><el-icon><Setting /></el-icon> 登录配置</h3>
                <p>根据目标网站的登录方式进行配置</p>
              </div>
              <el-form :model="taskForm" label-width="140px" class="login-form" size="large">
                <el-form-item label="目标页面类型">
                  <el-radio-group v-model="pageType" @change="onPageTypeChange">
                    <el-radio-button value="home">首页（需要登录）</el-radio-button>
                    <el-radio-button value="login">登录页面</el-radio-button>
                    <el-radio-button value="data">数据页面（无需登录）</el-radio-button>
                  </el-radio-group>
                  <div class="form-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>选择您输入的URL类型，帮助系统更好地处理页面</span>
                  </div>
                </el-form-item>
                
                <el-form-item label="登录类型">
                  <el-radio-group v-model="loginType" @change="onLoginTypeChange">
                    <el-radio-button value="none" :disabled="pageType === 'login'">无需登录</el-radio-button>
                    <el-radio-button value="form">表单登录</el-radio-button>
                    <el-radio-button value="cookie">Cookie登录</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                
                <!-- 表单登录配置 -->
                <template v-if="loginType === 'form'">
                  <div class="form-section-divider">
                    <el-divider><el-icon><User /></el-icon> 表单登录配置</el-divider>
                  </div>
                  <div class="form-login-tip">
                    <el-icon><Pointer /></el-icon>
                    <span>💡 小贴士：开启右侧的"点击选择模式"，然后点击页面中的表单元素可以自动获取选择器</span>
                  </div>
                  
                  <el-form-item label="用户名选择器">
                    <div class="selector-input-group">
                      <el-input
                        v-model="taskForm.loginConfig.usernameSelector"
                        placeholder="如：#username 或 input[name='username']"
                      />
                      <el-button
                        size="small"
                        :icon="View"
                        @click="validateLoginSelector('username')"
                        :loading="validatingLoginField === 'username'"
                        title="验证选择器"
                      />
                    </div>
                    <div v-if="loginValidations.username" class="validation-result">
                      <el-tag
                        :type="loginValidations.username.valid ? 'success' : 'danger'"
                        size="small"
                      >
                        <el-icon>
                          <CircleCheck v-if="loginValidations.username.valid" />
                          <CircleClose v-else />
                        </el-icon>
                        {{ loginValidations.username.valid 
                          ? `找到 ${loginValidations.username.count} 个元素` 
                          : '选择器无效' 
                        }}
                      </el-tag>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="密码选择器">
                    <div class="selector-input-group">
                      <el-input
                        v-model="taskForm.loginConfig.passwordSelector"
                        placeholder="如：#password 或 input[name='password']"
                      />
                      <el-button
                        size="small"
                        :icon="View"
                        @click="validateLoginSelector('password')"
                        :loading="validatingLoginField === 'password'"
                        title="验证选择器"
                      />
                    </div>
                    <div v-if="loginValidations.password" class="validation-result">
                      <el-tag
                        :type="loginValidations.password.valid ? 'success' : 'danger'"
                        size="small"
                      >
                        <el-icon>
                          <CircleCheck v-if="loginValidations.password.valid" />
                          <CircleClose v-else />
                        </el-icon>
                        {{ loginValidations.password.valid 
                          ? `找到 ${loginValidations.password.count} 个元素` 
                          : '选择器无效' 
                        }}
                      </el-tag>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="提交按钮选择器">
                    <div class="selector-input-group">
                      <el-input
                        v-model="taskForm.loginConfig.submitSelector"
                        placeholder="如：#submit 或 button[type='submit']（可选）"
                      />
                      <el-button
                        size="small"
                        :icon="View"
                        @click="validateLoginSelector('submit')"
                        :loading="validatingLoginField === 'submit'"
                        title="验证选择器"
                      />
                    </div>
                    <div v-if="loginValidations.submit" class="validation-result">
                      <el-tag
                        :type="loginValidations.submit.valid ? 'success' : 'danger'"
                        size="small"
                      >
                        <el-icon>
                          <CircleCheck v-if="loginValidations.submit.valid" />
                          <CircleClose v-else />
                        </el-icon>
                        {{ loginValidations.submit.valid 
                          ? `找到 ${loginValidations.submit.count} 个元素` 
                          : '选择器无效' 
                        }}
                      </el-tag>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="用户名">
                    <el-input
                      v-model="taskForm.loginConfig.username"
                      placeholder="请输入用户名"
                    />
                  </el-form-item>
                  
                  <el-form-item label="密码">
                    <el-input
                      v-model="taskForm.loginConfig.password"
                      type="password"
                      placeholder="请输入密码"
                      show-password
                    />
                  </el-form-item>
                  
                  <el-form-item label="登录后等待时间">
                    <el-input-number
                      v-model="taskForm.loginConfig.waitAfterLogin"
                      :min="0"
                      :max="30"
                      placeholder="秒"
                    />
                    <span class="form-tip-text">登录完成后额外等待时间（秒）</span>
                  </el-form-item>
                </template>
                
                <!-- Cookie登录配置 -->
                <template v-if="loginType === 'cookie'">
                  <el-form-item label="Cookie配置">
                    <div class="cookie-config">
                      <div 
                        v-for="(cookie, index) in taskForm.loginConfig.cookies"
                        :key="index"
                        class="cookie-item"
                      >
                        <el-input 
                          v-model="cookie.name" 
                          placeholder="Cookie名称"
                          style="width: 30%"
                        />
                        <el-input 
                          v-model="cookie.value" 
                          placeholder="Cookie值"
                          style="width: 50%"
                        />
                        <el-button 
                          @click="removeCookie(index)"
                          :icon="Delete"
                          type="danger"
                          size="small"
                        />
                      </div>
                      <el-button 
                        @click="addCookie"
                        :icon="Plus"
                        type="primary"
                        size="small"
                      >
                        添加Cookie
                      </el-button>
                    </div>
                  </el-form-item>
                </template>
              </el-form>
            </div>
            
            <!-- 右侧：页面预览 -->
            <div class="login-preview-section">
              <div class="preview-panel-header">
                <div class="preview-header-left">
                  <h3><el-icon><Monitor /></el-icon> 页面预览</h3>
                  <el-tag v-if="loginPagePreview" type="success" size="small">
                    <el-icon><CircleCheck /></el-icon>
                    已加载
                  </el-tag>
                </div>
                <div class="preview-controls">
                  <div class="control-group">
                    <el-switch
                      v-model="loginClickToSelect"
                      active-text="点击选择"
                      inactive-text="普通模式"
                      :disabled="!loginPagePreview || loginType !== 'form'"
                      size="small"
                    />
                    <el-button
                      size="small"
                      @click="loadLoginPagePreview"
                      :icon="Refresh"
                      :loading="previewLoading"
                      title="刷新预览"
                    />
                  </div>
                </div>
              </div>
              
              <div class="login-preview-container">
                <div v-if="!loginPagePreview && !previewLoading" class="preview-empty">
                  <div class="empty-content">
                    <el-icon class="empty-icon"><Monitor /></el-icon>
                    <h3>点击预览登录页面</h3>
                    <p>查看页面结构以便配置登录选择器</p>
                    <el-button 
                      type="primary" 
                      @click="loadLoginPagePreview"
                      :icon="Monitor"
                    >
                      预览登录页面
                    </el-button>
                  </div>
                </div>
                
                <div v-else-if="previewLoading" class="preview-loading">
                  <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h3>正在加载页面...</h3>
                    <p>请稍候，正在获取页面内容和处理重定向</p>
                    <div class="loading-progress">
                      <el-progress :percentage="loadingProgress" :show-text="false" />
                    </div>
                  </div>
                </div>
                
                <div v-else class="mini-preview">
                  <div v-if="loginPagePreview?.screenshot" class="preview-image-container">
                    <img
                      :src="loginPagePreview.screenshot"
                      alt="登录页面预览"
                      class="login-preview-screenshot"
                      @click="onLoginScreenshotClick"
                      :style="{ cursor: loginClickToSelect && loginType === 'form' ? 'crosshair' : 'default' }"
                    />
                    <div v-if="loginPagePreview.url !== taskForm.url" class="redirect-notice">
                      <el-alert
                        title="检测到页面重定向"
                        type="warning"
                        :closable="false"
                        show-icon
                      >
                        <template #default>
                          <p><strong>原始地址：</strong>{{ taskForm.url }}</p>
                          <p><strong>实际地址：</strong>{{ loginPagePreview.url }}</p>
                        </template>
                      </el-alert>
                    </div>
                  </div>
                  <div v-else class="preview-placeholder">
                    <el-icon class="placeholder-icon"><Monitor /></el-icon>
                    <h4>页面预览</h4>
                    <p>点击下方按钮加载页面预览</p>
                    <el-button 
                      type="primary" 
                      @click="loadLoginPagePreview" 
                      :loading="previewLoading"
                      :icon="View"
                    >
                      {{ previewLoading ? '加载中...' : '加载预览' }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3: 页面预览 -->
      <div v-show="currentStep === 2" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Monitor /></el-icon>
            <h2>页面预览</h2>
            <div class="step-actions">
              <el-button @click="prevStep" :icon="ArrowLeft">上一步</el-button>
              <el-button 
                type="primary" 
                @click="startSmartDetection" 
                :disabled="!pagePreview"
                :loading="smartDetecting"
                                 :icon="Star"
               >
                 {{ smartDetecting ? '智能识别中...' : '开始智能识别' }}
              </el-button>
            </div>
          </div>
          
          <div class="preview-section">
            <!-- 页面预览状态 -->
            <div v-if="!pagePreview && !previewLoading" class="preview-empty">
              <div class="empty-content">
                <el-icon class="empty-icon"><Monitor /></el-icon>
                <h3>页面还未加载</h3>
                <p>请返回上一步完成配置</p>
                <el-button @click="prevStep" :icon="ArrowLeft">返回上一步</el-button>
              </div>
            </div>
            
            <div v-else-if="previewLoading" class="preview-loading">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <h3>正在加载页面...</h3>
                <p>请稍候，正在获取页面内容</p>
                <div class="loading-progress">
                  <el-progress :percentage="loadingProgress" :show-text="false" />
                </div>
              </div>
            </div>
            
            <div v-else class="preview-container">
              <div class="preview-toolbar">
                <div class="toolbar-info">
                  <span class="site-title">{{ pagePreview?.title || '页面预览' }}</span>
                  <el-tag type="success" size="small">
                    <el-icon><CircleCheck /></el-icon>
                    加载成功
                  </el-tag>
                </div>
                <div class="toolbar-actions">
                  <el-button
                    size="small"
                    @click="refreshPreview"
                    :icon="Refresh"
                    :loading="previewLoading"
                  >
                    刷新预览
                  </el-button>
                </div>
              </div>
              
              <div class="screenshot-wrapper">
                <img
                  v-if="pagePreview?.screenshot"
                  :src="pagePreview.screenshot"
                  alt="页面预览"
                  class="preview-screenshot"
                  @click="onScreenshotClick"
                  style="cursor: crosshair"
                />
                <div v-else class="preview-placeholder">
                  <el-icon class="placeholder-icon"><Monitor /></el-icon>
                  <p>正在生成页面预览...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4: 智能识别 -->
      <div v-show="currentStep === 3" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Star /></el-icon>
            <h2>智能识别结果</h2>
            <div class="step-actions">
              <el-button @click="prevStep" :icon="ArrowLeft">上一步</el-button>
              <el-button 
                type="primary" 
                @click="nextStep" 
                :icon="ArrowRight"
              >
                继续配置字段
              </el-button>
            </div>
          </div>
          
          <div class="smart-detection-section">
            <div v-if="smartDetecting" class="detection-loading">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <h3>智能识别中...</h3>
                <p>正在分析页面结构，请稍候</p>
              </div>
            </div>
            
            <div v-else-if="smartDetection" class="detection-results">
              <!-- 表格识别结果 -->
              <div v-if="smartDetection.tables.length > 0" class="detection-category">
                <h3>
                  <el-icon><Grid /></el-icon>
                  检测到的表格 ({{ smartDetection.tables.length }})
                </h3>
                <div class="table-results">
                  <div 
                    v-for="(table, index) in smartDetection.tables" 
                    :key="`table-${index}`"
                    class="result-card"
                  >
                    <div class="result-header">
                      <h4>表格 {{ index + 1 }}</h4>
                      <el-button
                        type="primary"
                        size="small"
                        @click="applyTableFields(table)"
                      >
                        应用此表格
                      </el-button>
                    </div>
                    <div class="result-preview">
                      <div class="columns-info">
                        <span>列数：{{ table.columns.length }}</span>
                        <span>样本行数：{{ table.sampleData.length }}</span>
                      </div>
                      <div class="sample-table">
                        <table>
                          <thead>
                            <tr>
                              <th v-for="col in table.columns" :key="col.name">
                                {{ col.name }}
                                <el-tag size="small" :type="getFieldTypeColor(col.type)">
                                  {{ getFieldTypeLabel(col.type) }}
                                </el-tag>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(row, rowIndex) in table.sampleData.slice(0, 3)" :key="rowIndex">
                              <td v-for="col in table.columns" :key="col.name">
                                {{ row[col.name] || '-' }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 列表识别结果 -->
              <div v-if="smartDetection.lists.length > 0" class="detection-category">
                <h3>
                  <el-icon><List /></el-icon>
                  检测到的列表 ({{ smartDetection.lists.length }})
                </h3>
                <div class="list-results">
                  <div 
                    v-for="(list, index) in smartDetection.lists" 
                    :key="`list-${index}`"
                    class="result-card"
                  >
                    <div class="result-header">
                      <h4>列表 {{ index + 1 }}</h4>
                      <el-button
                        type="primary"
                        size="small"
                        @click="applyListFields(list)"
                      >
                        应用此列表
                      </el-button>
                    </div>
                    <div class="result-preview">
                      <div class="columns-info">
                        <span>字段数：{{ list.fields.length }}</span>
                        <span>样本项数：{{ list.sampleData.length }}</span>
                      </div>
                      <div class="sample-list">
                        <div class="field-tags">
                          <el-tag 
                            v-for="field in list.fields" 
                            :key="field.name"
                            :type="getFieldTypeColor(field.type)"
                            size="small"
                          >
                            {{ field.name }}
                          </el-tag>
                        </div>
                        <div class="sample-items">
                          <div 
                            v-for="(item, itemIndex) in list.sampleData.slice(0, 3)" 
                            :key="itemIndex"
                            class="sample-item"
                          >
                            <div v-for="field in list.fields" :key="field.name" class="item-field">
                              <strong>{{ field.name }}:</strong> {{ item[field.name] || '-' }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 没有检测到结果 -->
              <div v-if="smartDetection.tables.length === 0 && smartDetection.lists.length === 0" class="no-detection">
                <el-icon class="empty-icon"><Warning /></el-icon>
                <h3>未检测到明显的数据结构</h3>
                <p>您可以手动配置字段或尝试重新智能识别</p>
                <div class="no-detection-actions">
                  <el-button @click="startSmartDetection" :icon="Star">重新识别</el-button>
                  <el-button type="primary" @click="nextStep" :icon="ArrowRight">手动配置</el-button>
                </div>
              </div>
            </div>
            
            <div v-else class="detection-empty">
              <div class="empty-content">
                <el-icon class="empty-icon"><Star /></el-icon>
                <h3>还未开始智能识别</h3>
                <p>点击"开始智能识别"按钮分析页面结构</p>
                <el-button 
                  type="primary" 
                  @click="startSmartDetection" 
                  :icon="Star"
                  size="large"
                >
                  开始智能识别
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤5: 字段配置 -->
      <div v-show="currentStep === 4" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Collection /></el-icon>
            <h2>字段配置</h2>
            <div class="step-actions">
              <el-button @click="prevStep" :icon="ArrowLeft">上一步</el-button>
              <el-button 
                type="primary" 
                @click="nextStep" 
                :disabled="taskForm.fields.length === 0"
                :icon="ArrowRight"
              >
                完成配置
              </el-button>
            </div>
          </div>
          
          <div class="field-config-layout">
            <!-- 左侧：页面预览 -->
            <div class="preview-panel">
              <div class="panel-header">
                <h3>页面预览</h3>
                <div class="selector-controls">
                  <el-switch
                    v-model="clickToSelect"
                    active-text="点击选择模式"
                    inactive-text="普通模式"
                  />
                </div>
              </div>
              
              <div class="mini-preview">
                <img
                  v-if="pagePreview?.screenshot"
                  :src="pagePreview.screenshot"
                  alt="页面预览"
                  class="mini-screenshot"
                  @click="onMiniScreenshotClick"
                  :style="{ cursor: clickToSelect ? 'crosshair' : 'default' }"
                />
                <div v-else class="mini-placeholder">
                  <el-icon><Monitor /></el-icon>
                  <p>页面预览</p>
                </div>
              </div>
            </div>
            
            <!-- 右侧：字段配置 -->
            <div class="fields-panel">
              <div class="panel-header">
                <h3>
                  抓取字段 
                  <el-badge :value="taskForm.fields.length" class="field-count" />
                </h3>
                <el-button 
                  type="primary" 
                  :icon="Plus" 
                  @click="addField"
                  size="small"
                >
                  添加字段
                </el-button>
              </div>
              
              <div class="fields-list">
                <transition-group name="field-list" tag="div">
                  <div
                    v-for="(field, index) in taskForm.fields"
                    :key="`field-${index}`"
                    class="field-card"
                  >
                    <div class="field-header">
                      <div class="field-number">{{ index + 1 }}</div>
                      <el-button
                        size="small"
                        type="danger"
                        :icon="Delete"
                        circle
                        @click="removeField(index)"
                        class="delete-btn"
                      />
                    </div>
                    
                    <div class="field-content">
                      <el-form-item label="字段名称" class="field-form-item">
                        <el-input
                          v-model="field.fieldName"
                          placeholder="如：标题、链接、价格等"
                          size="small"
                        />
                      </el-form-item>
                      
                      <el-form-item label="CSS选择器" class="field-form-item">
                        <div class="selector-input-group">
                          <el-input
                            v-model="field.selector"
                            placeholder="如：.title, a.link"
                            size="small"
                          />
                          <el-button
                            size="small"
                            :icon="View"
                            @click="validateSelector(field.selector, index)"
                            :loading="validatingIndex === index"
                            title="验证选择器"
                          />
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="提取类型" class="field-form-item">
                        <el-select
                          v-model="field.type"
                          size="small"
                          style="width: 100%"
                        >
                          <el-option label="文本内容" value="text" />
                          <el-option label="链接地址" value="href" />
                          <el-option label="图片地址" value="src" />
                          <el-option label="属性值" value="attribute" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item 
                        v-if="field.type === 'attribute'" 
                        label="属性名" 
                        class="field-form-item"
                      >
                        <el-input
                          v-model="field.attribute"
                          placeholder="如：data-id、class等"
                          size="small"
                        />
                      </el-form-item>
                      
                      <div v-if="fieldValidations[index]" class="validation-result">
                        <el-tag
                          :type="fieldValidations[index].valid ? 'success' : 'danger'"
                          size="small"
                        >
                          <el-icon>
                            <CircleCheck v-if="fieldValidations[index].valid" />
                            <CircleClose v-else />
                          </el-icon>
                          {{ fieldValidations[index].valid 
                            ? `找到 ${fieldValidations[index].count} 个元素` 
                            : '选择器无效' 
                          }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </transition-group>
                
                <div v-if="taskForm.fields.length === 0" class="empty-fields">
                  <el-icon class="empty-icon"><Collection /></el-icon>
                  <p>还没有配置任何字段</p>
                  <p class="empty-tip">点击"添加字段"开始配置，或使用智能识别功能自动生成字段</p>
                </div>
              </div>
              
              <!-- 分页配置 -->
              <div class="pagination-config">
                <el-divider content-position="left">
                  <el-icon><Grid /></el-icon>
                  分页配置（可选）
                </el-divider>
                
                <el-form-item label="下一页选择器" class="pagination-form-item">
                  <el-input
                    v-model="taskForm.nextPageSelector"
                    placeholder="如：.next-page, a[rel='next']"
                    size="small"
                  >
                    <template #prefix>
                      <el-icon><ArrowRight /></el-icon>
                    </template>
                  </el-input>
                  <div class="form-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>配置后将自动翻页抓取所有数据（最多10页）</span>
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤6: 完成配置 -->
      <div v-show="currentStep === 5" class="step-panel">
        <div class="step-card">
          <div class="step-header">
            <el-icon class="step-icon"><Check /></el-icon>
            <h2>确认配置</h2>
            <div class="step-actions">
              <el-button @click="prevStep" :icon="ArrowLeft">返回修改</el-button>
            </div>
          </div>
          
          <div class="summary-section">
            <div class="summary-card">
              <h3>任务概览</h3>
              <div class="summary-item">
                <label>任务名称：</label>
                <span>{{ taskForm.name }}</span>
              </div>
              <div class="summary-item">
                <label>目标网址：</label>
                <span>{{ taskForm.url }}</span>
              </div>
              <div class="summary-item">
                <label>登录配置：</label>
                <span>{{ getLoginConfigText() }}</span>
              </div>
              <div class="summary-item">
                <label>抓取字段：</label>
                <span>{{ taskForm.fields.length }} 个字段</span>
              </div>
              <div class="summary-item" v-if="taskForm.nextPageSelector">
                <label>分页配置：</label>
                <span>已启用自动翻页</span>
              </div>
            </div>
            
            <div class="fields-summary">
              <h3>字段详情</h3>
              <div class="field-summary-list">
                <div v-for="(field, index) in taskForm.fields" :key="index" class="field-summary-item">
                  <div class="field-summary-header">
                    <span class="field-summary-number">{{ index + 1 }}</span>
                    <span class="field-summary-name">{{ field.fieldName }}</span>
                    <el-tag size="small" :type="getFieldTypeColor(field.type)">
                      {{ getFieldTypeLabel(field.type) }}
                    </el-tag>
                  </div>
                  <div class="field-summary-selector">{{ field.selector }}</div>
                </div>
              </div>
            </div>
            
            <div class="final-actions">
              <el-button 
                type="primary" 
                @click="saveTask" 
                :loading="saving"
                :icon="Check"
                size="large"
                class="final-save-btn"
              >
                {{ isEdit ? '更新任务' : '保存任务' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Delete,
  View,
  ArrowLeft,
  ArrowRight,
  Check,
  Setting,
  InfoFilled,
  Monitor,
  Collection,
  Grid,
  CircleCheck,
  CircleClose,
  Refresh,
  Key,
  Star,
  List,
  Warning,
  User,
  Pointer
} from '@element-plus/icons-vue'
import { taskApi, scraperApi } from '@/api'
import type { 
  TaskFormData, 
  ScrapingField, 
  PagePreview, 
  SelectorValidation, 
  LoginConfig,
  SmartDetectionResult,
  ElementSelection
} from '@/types'

const router = useRouter()
const route = useRoute()

const isEdit = ref(false)
const saving = ref(false)
const previewLoading = ref(false)
const smartDetecting = ref(false)
const validatingIndex = ref(-1)
const currentStep = ref(0)
const clickToSelect = ref(false)
const loadingProgress = ref(0)

// 登录配置相关
const loginType = ref<'none' | 'form' | 'cookie'>('none')
const loginClickToSelect = ref(false)
const pageType = ref<'home' | 'login' | 'data'>('home')

const taskForm = reactive<TaskFormData>({
  name: '',
  url: '',
  fields: [],
  nextPageSelector: '',
  loginConfig: {
    type: 'none',
    cookies: []
  },
  preActions: []
})

const pagePreview = ref<PagePreview | null>(null)
const smartDetection = ref<SmartDetectionResult | null>(null)
const fieldValidations = ref<Record<number, SelectorValidation>>({})
const lastPreviewUrl = ref('')

// 登录预览相关
const loginPagePreview = ref<PagePreview | null>(null)
const loginValidations = reactive<Record<string, SelectorValidation>>({})
const validatingLoginField = ref('')

// 计算属性
const canProceedToNext = computed(() => {
  return taskForm.name.trim() && taskForm.url.trim()
})

// 登录配置相关方法
const onPageTypeChange = (type: 'home' | 'login' | 'data') => {
  if (type === 'data') {
    loginType.value = 'none'
  } else if (type === 'login') {
    loginType.value = 'form'
  }
  // 清除之前的预览
  clearPreviewCache()
}

const onLoginTypeChange = (type: 'none' | 'form' | 'cookie') => {
  taskForm.loginConfig = {
    type,
    ...(type === 'cookie' && { cookies: [] })
  }
  // 登录配置变化时清除预览缓存
  clearPreviewCache()
}

// 清除预览缓存
const clearPreviewCache = () => {
  loginPagePreview.value = null
  pagePreview.value = null
  lastPreviewUrl.value = ''
  loadingProgress.value = 0
}

const addCookie = () => {
  if (!taskForm.loginConfig?.cookies) {
    taskForm.loginConfig.cookies = []
  }
  taskForm.loginConfig.cookies.push({
    name: '',
    value: ''
  })
}

const removeCookie = (index: number) => {
  if (taskForm.loginConfig?.cookies) {
    taskForm.loginConfig.cookies.splice(index, 1)
  }
}

// 步骤控制
const nextStep = () => {
  if (currentStep.value < 5) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 加载页面并前进到下一步
const loadPageAndNext = async () => {
  const success = await loadPagePreview()
  if (success) {
    nextStep()
  }
}

// 加载登录页面预览
const loadLoginPagePreview = async (forceReload = false) => {
  const result = await loadPagePreview(forceReload)
  if (result && pagePreview.value) {
    // 将主预览复制到登录预览
    loginPagePreview.value = pagePreview.value
  }
  return result
}

// 继续到下一步（登录配置）
const proceedToNextStep = async () => {
  if (loginType.value === 'form') {
    if (!taskForm.loginConfig?.usernameSelector) {
      ElMessage.warning('请先配置用户名选择器')
      return
    }
    if (!taskForm.loginConfig?.passwordSelector) {
      ElMessage.warning('请先配置密码选择器')
      return
    }
  }

  // 确保在进入下一步之前有页面预览
  if (!pagePreview.value) {
    const success = await loadPagePreview()
    if (!success) {
      ElMessage.error('加载页面预览失败，请检查URL或网络连接')
      return
    }
  }

  nextStep()
}

// 统一的页面预览加载方法
const loadPagePreview = async (forceReload = false) => {
  if (!taskForm.url) {
    ElMessage.warning('请先输入URL')
    return false
  }

  // 避免重复请求
  if (!forceReload && lastPreviewUrl.value === taskForm.url && pagePreview.value) {
    return true
  }

  previewLoading.value = true
  loadingProgress.value = 20

  try {
    console.log('开始加载页面预览:', taskForm.url)

    const loginConfig = taskForm.loginConfig?.type !== 'none' ? taskForm.loginConfig : undefined
    const response = await scraperApi.getPagePreview(taskForm.url, true, loginConfig)

    console.log('API响应:', response)
    loadingProgress.value = 80

    if (response && response.success && response.data) {
      pagePreview.value = response.data
      lastPreviewUrl.value = taskForm.url
      loadingProgress.value = 100
      ElMessage.success('页面预览加载成功')
      return true
    } else {
      console.error('API响应错误:', response)
      ElMessage.error(response?.error || '预览加载失败')
      return false
    }
  } catch (error: any) {
    console.error('预览加载失败:', error)
    ElMessage.error(`加载失败: ${error.message || '未知错误'}`)
    return false
  } finally {
    previewLoading.value = false
    loadingProgress.value = 0
  }
}

// 错误信息处理
const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.error) {
    return error.response.data.error
  }
  if (error?.error) {
    return error.error
  }
  if (error?.message) {
    if (error.message.includes('timeout')) {
      return '页面加载超时，请检查网络连接或URL是否正确'
    }
    if (error.message.includes('ERR_NAME_NOT_RESOLVED')) {
      return 'URL地址无法解析，请检查网址是否正确'
    }
    if (error.message.includes('ERR_CONNECTION_REFUSED')) {
      return '无法连接到目标网站，请检查网址或稍后重试'
    }
    return `加载失败: ${error.message}`
  }
  return '页面预览加载失败，请检查URL或网络连接'
}

// 开始智能识别
const startSmartDetection = async () => {
  if (!taskForm.url) {
    ElMessage.warning('请先输入URL')
    return
  }
  
  try {
    smartDetecting.value = true
    const response = await scraperApi.smartDetectPageStructure(
      taskForm.url, 
      taskForm.loginConfig?.type !== 'none' ? taskForm.loginConfig : undefined
    )
    
    if (response.success && response.data) {
      smartDetection.value = response.data
      nextStep()
      ElMessage.success('智能识别完成')
    }
  } catch (error: any) {
    ElMessage.error(`智能识别失败: ${error?.error || error?.message || 'Unknown error'}`)
  } finally {
    smartDetecting.value = false
  }
}

// 应用表格字段
const applyTableFields = (table: any) => {
  taskForm.fields = table.columns.map((col: any) => ({
    fieldName: col.name,
    selector: col.selector,
    type: col.type,
    attribute: col.type === 'attribute' ? col.attribute : undefined
  }))
  ElMessage.success(`已应用表格字段，共 ${table.columns.length} 个字段`)
  nextStep()
}

// 应用列表字段
const applyListFields = (list: any) => {
  taskForm.fields = list.fields.map((field: any) => ({
    fieldName: field.name,
    selector: field.selector,
    type: field.type,
    attribute: field.type === 'attribute' ? field.attribute : undefined
  }))
  ElMessage.success(`已应用列表字段，共 ${list.fields.length} 个字段`)
  nextStep()
}

// 刷新预览
const refreshPreview = () => {
  pagePreview.value = null
  loginPagePreview.value = null
  lastPreviewUrl.value = ''
  loadPagePreview(true)
}

// 获取字段类型标签
const getFieldTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    text: '文本',
    href: '链接',
    src: '图片',
    attribute: '属性'
  }
  return labels[type] || type
}

const getFieldTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    text: '',
    href: 'success',
    src: 'warning',
    attribute: 'info'
  }
  return colors[type] || ''
}

// 获取登录配置文本
const getLoginConfigText = () => {
  switch (taskForm.loginConfig?.type) {
    case 'form':
      return '表单登录'
    case 'cookie':
      return 'Cookie登录'
    default:
      return '无需登录'
  }
}



// URL变化时的处理
const onUrlChange = () => {
  pagePreview.value = null
  loginPagePreview.value = null
  smartDetection.value = null
  fieldValidations.value = {}
  lastPreviewUrl.value = ''
  loadingProgress.value = 0
}

// 截图点击事件
const onScreenshotClick = async (event: MouseEvent) => {
  if (!clickToSelect) return
  
  const rect = (event.target as HTMLImageElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 计算相对于原图的坐标
  const scaleX = pagePreview.value!.viewport.width / rect.width
  const scaleY = pagePreview.value!.viewport.height / rect.height
  
  const actualX = x * scaleX
  const actualY = y * scaleY
  
  try {
    const response = await scraperApi.getElementAtCoordinate(
      taskForm.url,
      actualX,
      actualY,
      taskForm.loginConfig?.type !== 'none' ? taskForm.loginConfig : undefined
    )
    
    if (response.success && response.data) {
      const element = response.data
      addField({
        fieldName: element.suggestedField.name,
        selector: element.selector,
        type: element.suggestedField.type,
        attribute: element.suggestedField.attribute
      })
      ElMessage.success(`已添加字段：${element.suggestedField.name}`)
    }
  } catch (error: any) {
    ElMessage.error(`获取元素失败: ${error?.error || error?.message || 'Unknown error'}`)
  }
}

// 小预览图点击事件
const onMiniScreenshotClick = (event: MouseEvent) => {
  onScreenshotClick(event)
}

// 登录页面截图点击事件
const onLoginScreenshotClick = async (event: MouseEvent) => {
  if (!loginClickToSelect) return
  
  const rect = (event.target as HTMLImageElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 计算相对于原图的坐标
  const scaleX = loginPagePreview.value!.viewport.width / rect.width
  const scaleY = loginPagePreview.value!.viewport.height / rect.height
  
  const actualX = x * scaleX
  const actualY = y * scaleY
  
  try {
    const response = await scraperApi.getElementAtCoordinate(
      taskForm.url,
      actualX,
      actualY,
      taskForm.loginConfig?.type !== 'none' ? taskForm.loginConfig : undefined
    )
    
    if (response.success && response.data) {
      const element = response.data
      
      // 根据元素类型智能设置对应的登录选择器
      const tagName = element.element.tagName.toLowerCase()
      const elementType = element.element.attributes.type || ''
      
      if (tagName === 'input') {
        if (elementType === 'password' || element.element.attributes.name?.includes('password')) {
          // 密码输入框
          taskForm.loginConfig.passwordSelector = element.selector
          ElMessage.success('已设置密码选择器')
        } else if (elementType === 'text' || elementType === 'email' || 
                  element.element.attributes.name?.includes('username') ||
                  element.element.attributes.name?.includes('email')) {
          // 用户名输入框
          taskForm.loginConfig.usernameSelector = element.selector
          ElMessage.success('已设置用户名选择器')
        } else {
          // 其他输入框，让用户选择
          showSelectorTypeDialog(element.selector)
        }
      } else if (tagName === 'button' || (tagName === 'input' && elementType === 'submit')) {
        // 提交按钮
        taskForm.loginConfig.submitSelector = element.selector
        ElMessage.success('已设置提交按钮选择器')
      } else {
        // 其他元素，让用户选择用途
        showSelectorTypeDialog(element.selector)
      }
    }
  } catch (error: any) {
    ElMessage.error(`获取元素失败: ${error?.error || error?.message || 'Unknown error'}`)
  }
}

// 显示选择器类型对话框
const showSelectorTypeDialog = async (selector: string) => {
  try {
    const { value } = await ElMessageBox.prompt(
      '请选择该选择器的用途:\n1 - 用户名输入框\n2 - 密码输入框\n3 - 提交按钮\n\n请输入数字(1-3):', 
      '选择器用途',
      {
        inputType: 'text',
        inputValidator: (value: string) => {
          const num = parseInt(value)
          if (num >= 1 && num <= 3) {
            return true
          }
          return '请输入1-3之间的数字'
        }
      }
    )
    
    const choice = parseInt(value)
    switch (choice) {
      case 1:
        taskForm.loginConfig.usernameSelector = selector
        ElMessage.success('已设置用户名选择器')
        break
      case 2:
        taskForm.loginConfig.passwordSelector = selector
        ElMessage.success('已设置密码选择器')
        break
      case 3:
        taskForm.loginConfig.submitSelector = selector
        ElMessage.success('已设置提交按钮选择器')
        break
    }
  } catch (error) {
    // 用户取消了选择
  }
}

// 添加字段
const addField = (field?: Partial<ScrapingField>) => {
  const newField: ScrapingField = {
    fieldName: field?.fieldName || '新字段',
    selector: field?.selector || '',
    type: field?.type || 'text',
    attribute: field?.attribute
  }
  
  taskForm.fields.push(newField)
}

// 删除字段
const removeField = (index: number) => {
  taskForm.fields.splice(index, 1)
  delete fieldValidations.value[index]
}

// 验证选择器
const validateSelector = async (selector: string, index: number) => {
  if (!selector || !taskForm.url) {
    return
  }
  
  try {
    validatingIndex.value = index
    const response = await scraperApi.validateSelector(taskForm.url, selector)
    
    if (response.success && response.data) {
      fieldValidations.value[index] = response.data
    }
  } catch (error) {
    ElMessage.error('验证选择器失败')
    console.error('验证选择器失败:', error)
  } finally {
    validatingIndex.value = -1
  }
}

// 验证登录选择器
const validateLoginSelector = async (fieldType: string) => {
  if (!taskForm.url) {
    ElMessage.warning('请先输入URL')
    return
  }

  if (!taskForm.loginConfig) {
    ElMessage.warning('请先选择登录类型')
    return
  }

  if (taskForm.loginConfig.type !== 'form') {
    ElMessage.warning('当前登录类型不支持此功能')
    return
  }

  // 获取对应的选择器值
  let selector: string | undefined
  switch (fieldType) {
    case 'username':
      selector = taskForm.loginConfig.usernameSelector
      break
    case 'password':
      selector = taskForm.loginConfig.passwordSelector
      break
    case 'submit':
      selector = taskForm.loginConfig.submitSelector
      break
    default:
      ElMessage.error('不支持的字段类型')
      return
  }

  if (!selector) {
    ElMessage.warning('请先输入选择器')
    return
  }

  try {
    validatingLoginField.value = fieldType
    const response = await scraperApi.validateSelector(taskForm.url, selector)
    
    if (response.success && response.data) {
      loginValidations[fieldType] = response.data
    }
  } catch (error) {
    ElMessage.error('验证选择器失败')
    console.error('验证选择器失败:', error)
  } finally {
    validatingLoginField.value = ''
  }
}

// 保存任务
const saveTask = async () => {
  // 验证表单
  if (!taskForm.name.trim()) {
    ElMessage.warning('请输入任务名称')
    return
  }
  
  if (!taskForm.url.trim()) {
    ElMessage.warning('请输入目标网址')
    return
  }
  
  if (taskForm.fields.length === 0) {
    ElMessage.warning('请至少添加一个抓取字段')
    return
  }
  
  // 验证字段
  for (const field of taskForm.fields) {
    if (!field.fieldName.trim()) {
      ElMessage.warning('字段名称不能为空')
      return
    }
    if (!field.selector.trim()) {
      ElMessage.warning('选择器不能为空')
      return
    }
  }
  
  try {
    saving.value = true
    
    if (isEdit.value) {
      const response = await taskApi.updateTask(route.params.id as string, taskForm)
      if (response.success) {
        ElMessage.success('任务更新成功')
        router.push('/tasks')
      }
    } else {
      const response = await taskApi.createTask(taskForm)
      if (response.success) {
        ElMessage.success('任务创建成功')
        router.push('/tasks')
      }
    }
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新任务失败' : '创建任务失败')
    console.error('保存任务失败:', error)
  } finally {
    saving.value = false
  }
}

// 返回
const goBack = () => {
  router.push('/tasks')
}

// 加载任务数据（编辑模式）
const loadTaskData = async () => {
  const taskId = route.params.id as string
  if (!taskId) return
  
  try {
    const response = await taskApi.getTask(taskId)
    if (response.success && response.data) {
      const task = response.data
      taskForm.name = task.name
      taskForm.url = task.url
      taskForm.fields = [...task.fields]
      taskForm.nextPageSelector = task.nextPageSelector || ''
      taskForm.loginConfig = task.loginConfig || { type: 'none' }
      loginType.value = taskForm.loginConfig.type
      loginClickToSelect.value = false // 编辑模式下不启用点击选择模式
      
      // 加载页面预览
      await nextTick()
      loadPagePreview()
      loadLoginPagePreview() // 加载登录页面预览
    }
  } catch (error) {
    ElMessage.error('加载任务数据失败')
    console.error('加载任务数据失败:', error)
  }
}

onMounted(() => {
  isEdit.value = !!route.params.id
  
  if (isEdit.value) {
    loadTaskData()
    // 编辑模式下直接跳到最后一步
    currentStep.value = 5
  }
})
</script>

<style scoped>
/* 主布局 */
.task-config-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-center {
  flex: 1;
  text-align: center;
  margin: 0 32px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

.back-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  font-weight: 600;
}

.save-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 步骤指示器 */
.steps-container {
  padding: 32px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.config-steps {
  max-width: 900px;
  margin: 0 auto;
}

/* 主要内容区域 */
.config-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px;
}

.step-panel {
  animation: fadeInUp 0.5s ease-out;
}

.step-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.step-header {
  padding: 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.step-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-icon {
  font-size: 28px;
  color: #4f46e5;
}

.step-actions {
  display: flex;
  gap: 12px;
}

/* 步骤1: 基本信息 */
.basic-form {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.basic-form .el-form-item {
  margin-bottom: 32px;
}

.basic-form .el-form-item__label {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.form-input {
  border-radius: 12px;
  font-size: 16px;
}

.url-input-container {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  width: 100%;
}

.url-input-container .el-input {
  flex: 1;
}

.next-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  font-weight: 600;
  white-space: nowrap;
  min-width: 180px;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 12px 16px;
  background: #f1f5f9;
  border-radius: 8px;
  color: #64748b;
  font-size: 14px;
}

.form-tip .el-icon {
  color: #3b82f6;
}

/* 步骤2: 登录配置 */
.login-config-section {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-form .el-form-item__label {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.login-form .el-radio-group {
  display: flex;
  gap: 12px;
}

.cookie-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.cookie-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.form-tip-text {
  font-size: 14px;
  color: #64748b;
  margin-left: 8px;
}

/* 步骤3: 页面预览 */
.preview-section {
  padding: 32px;
}

.preview-empty,
.preview-loading {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.empty-content,
.loading-content {
  text-align: center;
  padding: 40px;
}

.empty-icon {
  font-size: 80px;
  color: #cbd5e1;
  margin-bottom: 24px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f1f5f9;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-container {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.preview-toolbar {
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.site-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
}

.screenshot-wrapper {
  height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.preview-screenshot {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--color-text-secondary);
}

.placeholder-icon {
  font-size: 48px;
  color: var(--color-text-muted);
}

/* 步骤4: 智能识别 */
.smart-detection-section {
  padding: 32px;
}

.detection-loading,
.detection-empty {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.detection-results {
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  max-height: 500px;
  overflow-y: auto;
}

.detection-category {
  margin-bottom: 32px;
}

.detection-category h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.detection-loading .loading-content,
.detection-empty .empty-content {
  text-align: center;
  padding: 40px;
}

.detection-loading .loading-spinner {
  margin: 0 auto 24px;
}

.result-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.result-preview {
  padding: 16px;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.columns-info {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #64748b;
}

.sample-table {
  overflow-x: auto;
}

.sample-table table {
  border-collapse: collapse;
  width: 100%;
  min-width: 600px;
}

.sample-table th,
.sample-table td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.sample-table th {
  background-color: #f8fafc;
  font-weight: 600;
}

.sample-table th .el-tag {
  margin-left: 8px;
}

.sample-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.sample-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sample-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.item-field {
  font-size: 14px;
  color: #4b5563;
  display: flex;
  gap: 8px;
}

.item-field strong {
  font-weight: 600;
  color: #1e293b;
  min-width: 60px;
}

.no-detection {
  text-align: center;
  padding: 40px;
}

.no-detection .empty-icon {
  font-size: 80px;
  color: #cbd5e1;
  margin-bottom: 24px;
}

.no-detection h3 {
  margin-bottom: 12px;
}

.no-detection-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}

.detection-empty .empty-icon {
  font-size: 80px;
  color: #cbd5e1;
  margin-bottom: 24px;
}

.detection-empty .empty-content h3 {
  margin-bottom: 12px;
}

/* 步骤5: 字段配置 */
.field-config-layout {
  display: grid;
  grid-template-columns: 1fr 480px;
  gap: 24px;
  padding: 32px;
  min-height: 700px;
}

.preview-panel {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.panel-header {
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.panel-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.selector-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selector-tip {
  margin: 0;
}

.mini-preview {
  height: 550px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--color-bg-secondary);
  overflow: hidden;
}

.mini-screenshot {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

.mini-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
}

.mini-placeholder .el-icon {
  font-size: 32px;
  color: var(--color-text-muted);
}

.mini-placeholder p {
  margin: 0;
  font-size: 14px;
}

.fields-panel {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.field-count .el-badge__content {
  background: #4f46e5;
}

.fields-list {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  max-height: 450px;
}

.field-card {
  border: 2px solid #f1f5f9;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  background: #fafbfc;
  transition: all 0.3s ease;
  position: relative;
}

.field-card:hover {
  border-color: #4f46e5;
  background: white;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.field-number {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
}

.field-form-item {
  margin-bottom: 16px;
}

.field-form-item .el-form-item__label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.selector-input-group {
  display: flex;
  gap: 8px;
}

.selector-input-group .el-input {
  flex: 1;
}

.validation-result {
  margin-top: 12px;
}

.empty-fields {
  text-align: center;
  padding: 60px 20px;
  color: #94a3b8;
}

.empty-fields .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-tip {
  font-size: 14px;
  line-height: 1.5;
}

.pagination-config {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f1f5f9;
}

/* 步骤6: 完成配置 */
.summary-section {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.summary-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.summary-card h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.summary-item {
  display: flex;
  margin-bottom: 12px;
  font-size: 16px;
}

.summary-item label {
  font-weight: 600;
  color: #374151;
  min-width: 120px;
}

.fields-summary {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid #e2e8f0;
}

.field-summary-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-summary-item {
  border: 1px solid #f1f5f9;
  border-radius: 8px;
  padding: 16px;
  background: #fafbfc;
}

.field-summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.field-summary-number {
  background: #4f46e5;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.field-summary-name {
  font-weight: 600;
  color: #1e293b;
}

.field-summary-selector {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  color: #64748b;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.final-actions {
  text-align: center;
}

.final-save-btn {
  background: linear-gradient(135deg, #059669 0%, #16a34a 100%);
  border: none;
  font-weight: 600;
  font-size: 18px;
  padding: 12px 32px;
  box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3);
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.field-list-enter-active,
.field-list-leave-active {
  transition: all 0.3s ease;
}

.field-list-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.field-list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 步骤2: 登录配置新布局 */
.login-config-layout {
  display: grid;
  grid-template-columns: 1fr 480px;
  gap: 24px;
  padding: 32px;
  min-height: 600px;
}

.login-form-section {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  padding: 24px;
}

.login-preview-section {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  display: flex;
  flex-direction: column;
}

.preview-panel-header {
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 6px;
}

.preview-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-controls .control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.preview-image-container {
  width: 100%;
}

.login-preview-screenshot {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
}

.redirect-notice {
  margin-top: 12px;
}

.redirect-notice .el-alert {
  border-radius: 6px;
}

.redirect-notice p {
  margin: 4px 0;
  font-size: 13px;
  word-break: break-all;
}

.loading-progress {
  margin-top: 16px;
  width: 200px;
}

.form-login-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 14px;
}

.form-login-tip .el-icon {
  color: #0284c7;
}

.selector-input-group {
  display: flex;
  gap: 8px;
}

.selector-input-group .el-input {
  flex: 1;
}

.validation-result {
  margin-top: 8px;
}

/* 步骤导航样式 */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left, .action-right {
  display: flex;
  gap: 12px;
}

/* 登录配置向导样式 */
.guide-content {
  padding: 16px 0;
}

.guide-section {
  margin-bottom: 20px;
}

.guide-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
}

.guide-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.guide-description {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  margin-left: 20px;
}

/* 表单区域样式 */
.form-section-header {
  margin-bottom: 16px;
}

.form-section-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-section-header p {
  margin: 0;
  font-size: 13px;
  color: #909399;
}

.form-section-divider {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 20px 0 16px 0;
  padding: 8px 0;
  border-top: 1px solid #ebeef5;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

/* 响应式设计 - 登录配置 */
@media (max-width: 1200px) {
  .login-config-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .login-preview-section {
    order: -1;
  }
  
  .login-preview-container {
    min-height: 400px;
  }
  
  .step-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .action-left, .action-right {
    width: 100%;
    justify-content: center;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .field-config-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .fields-panel {
    order: -1;
  }
  
  .mini-preview {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-center {
    margin: 0;
  }
  
  .config-content {
    padding: 16px;
  }
  
  .step-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .url-input-container {
    flex-direction: column;
  }
  
  .next-btn {
    width: 100%;
    min-width: auto;
  }
}
</style>