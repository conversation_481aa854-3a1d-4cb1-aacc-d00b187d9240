import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ScrapingResult, LoginConfig, PageInteraction, SmartDetectionResult, PageScreenshot, ElementSelection } from '../types';
import { StorageService } from './storage';

export class ScraperService {
  private static instance: ScraperService;
  private browser: Browser | null = null;
  private storage = StorageService.getInstance();

  static getInstance(): ScraperService {
    if (!ScraperService.instance) {
      ScraperService.instance = new ScraperService();
    }
    return ScraperService.instance;
  }

  async initBrowser(): Promise<void> {
    if (!this.browser) {
      try {
        this.browser = await puppeteer.launch({
          headless: true,
          executablePath: puppeteer.executablePath(),
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--window-size=1920,1080' // 更大的窗口尺寸
          ]
        });
      } catch (error) {
        console.error('启动浏览器失败:', error);
        try {
          this.browser = await puppeteer.launch({
            headless: true,
            args: [
              '--no-sandbox',
              '--disable-setuid-sandbox',
              '--disable-dev-shm-usage',
              '--disable-web-security',
              '--disable-features=VizDisplayCompositor',
              '--single-process'
            ],
            ignoreDefaultArgs: ['--disable-extensions']
          });
        } catch (fallbackError) {
          console.error('备用配置也失败:', fallbackError);
          throw new Error(`无法启动浏览器: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);
        }
      }
    }
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // 增强的页面预览功能
  async getPagePreview(url: string, fullPage: boolean = true, loginConfig?: LoginConfig): Promise<PageScreenshot> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      // 设置更大的视口
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 处理登录
      if (loginConfig) {
        await this.handleLogin(page, url, loginConfig);
      } else {
        await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      }
      
      const title = await page.title();
      const currentUrl = page.url();
      
      // 获取页面滚动高度，用于后续的全屏截图
      // @ts-ignore - page.evaluate中的DOM API是正确的
      const scrollHeight = await page.evaluate(() => document.documentElement.scrollHeight);
      
      // 处理页面滚动和动画
      await this.handlePageScrollingAndAnimations(page);
      
      // 截图（根据fullPage参数决定是否全屏）
      const screenshotBuffer = await page.screenshot({ 
        fullPage, 
        type: 'png',
        encoding: 'base64'
      });
      
      return {
        title,
        url: currentUrl,
        screenshot: `data:image/png;base64,${screenshotBuffer}`,
        viewport: { width: 1920, height: 1080 },
        fullPage,
        scrollHeight
      };
    } finally {
      await page.close();
    }
  }

  // 优化：处理页面加载和基础准备，减少不必要的等待
  private async handlePageScrollingAndAnimations(page: Page): Promise<void> {
    try {
      // 等待基础DOM加载完成
      await page.waitForSelector('body', { timeout: 8000 });

      // 简化网络等待逻辑
      try {
        await page.waitForLoadState('networkidle', { timeout: 5000 });
      } catch (error) {
        console.log('网络空闲等待超时，继续处理');
      }
      
      // 优化：快速处理弹窗和遮罩
      await page.evaluate(() => {
        // 常见的遮罩层选择器
        const overlaySelectors = [
          '.overlay', '.modal', '.popup', '.mask', '.loading-mask',
          '[class*="overlay"]', '[class*="modal"]', '[class*="popup"]'
        ];

        overlaySelectors.forEach((selector) => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((el: any) => {
            const style = window.getComputedStyle(el);
            if (style.position === 'fixed' || style.position === 'absolute') {
              if (style.zIndex && parseInt(style.zIndex) > 100) {
                (el as HTMLElement).style.display = 'none';
              }
            }
          });
        });

        // 尝试关闭常见的弹窗
        const closeButtons = document.querySelectorAll('[class*="close"], [class*="dismiss"], .btn-close');
        closeButtons.forEach((btn: any) => {
          if (btn instanceof HTMLElement && btn.offsetParent !== null) {
            try {
              btn.click();
            } catch (e) {
              // 忽略点击错误
            }
          }
        });
      });

      // 减少等待时间
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 获取页面高度并进行智能滚动
      // @ts-ignore - page.evaluate中的DOM API是正确的
      const { totalHeight, viewportHeight } = await page.evaluate(() => {
        return {
          totalHeight: Math.max(
            document.documentElement.scrollHeight,
            document.documentElement.offsetHeight,
            document.body.scrollHeight,
            document.body.offsetHeight
          ),
          viewportHeight: window.innerHeight
        };
      });
      
      console.log(`页面总高度: ${totalHeight}, 视口高度: ${viewportHeight}`);

      // 优化：只在页面很长时才进行滚动，减少滚动步骤和等待时间
      if (totalHeight > viewportHeight * 2) {
        // 减少滚动步骤，提高加载速度
        const scrollSteps = Math.min(3, Math.ceil(totalHeight / (viewportHeight * 2)));
        const stepHeight = totalHeight / scrollSteps;

        for (let i = 1; i <= scrollSteps; i++) {
          const targetY = Math.min(i * stepHeight, totalHeight);

          await page.evaluate((y) => {
            window.scrollTo({
              top: y,
              behavior: 'auto' // 使用auto而不是smooth，减少等待时间
            });
          }, targetY);

          // 减少等待时间
          await new Promise(resolve => setTimeout(resolve, 300));
        }

        // 快速回到顶部
        await page.evaluate(() => {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        });

        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // 确保页面在顶部（如果之前没有滚动的话）
      if (totalHeight <= viewportHeight * 2) {
        await page.evaluate(() => {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          });
        });

        // 减少等待时间
        await new Promise(resolve => setTimeout(resolve, 800));
      }
      
      // 检查页面是否还在加载
      // @ts-ignore - page.evaluate中的DOM API是正确的
      const isLoading = await page.evaluate(() => {
        // 检查常见的加载指示器
        const loadingIndicators = document.querySelectorAll(
          '.loading, .spinner, [class*="loading"], [class*="spinner"]'
        );
        
        return Array.from(loadingIndicators).some((indicator: any) => {
          const style = window.getComputedStyle(indicator);
          return style.display !== 'none' && style.visibility !== 'hidden';
        });
      });
      
      if (isLoading) {
        console.log('检测到页面仍在加载，等待完成...');
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
      
    } catch (error) {
      console.log('页面滚动处理出错，继续截图:', error);
      // 即使出错也要确保回到顶部
      try {
        // @ts-ignore - page.evaluate中的window API是正确的
        await page.evaluate(() => window.scrollTo(0, 0));
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (e) {
        console.log('回到顶部失败:', e);
      }
    }
  }

  // 新增：处理登录
  private async handleLogin(page: Page, url: string, loginConfig: LoginConfig): Promise<void> {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
    
    switch (loginConfig.type) {
      case 'form':
        if (loginConfig.usernameSelector && loginConfig.passwordSelector && 
            loginConfig.username && loginConfig.password) {
          
          // 输入用户名
          await page.waitForSelector(loginConfig.usernameSelector, { timeout: 10000 });
          await page.type(loginConfig.usernameSelector, loginConfig.username);
          
          // 输入密码
          await page.waitForSelector(loginConfig.passwordSelector, { timeout: 10000 });
          await page.type(loginConfig.passwordSelector, loginConfig.password);
          
          // 点击提交按钮
          if (loginConfig.submitSelector) {
            await page.click(loginConfig.submitSelector);
          } else {
            // 尝试按回车提交
            await page.keyboard.press('Enter');
          }
          
          // 等待登录完成
          await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 }).catch(() => {
            console.log('登录后没有页面跳转，继续执行');
          });
          
          // 额外等待时间
          if (loginConfig.waitAfterLogin) {
            await new Promise(resolve => setTimeout(resolve, loginConfig.waitAfterLogin! * 1000));
          }
        }
        break;
        
      case 'cookie':
        if (loginConfig.cookies && loginConfig.cookies.length > 0) {
          // 设置Cookie
          for (const cookie of loginConfig.cookies) {
            await page.setCookie({
              name: cookie.name,
              value: cookie.value,
              domain: cookie.domain || new URL(url).hostname,
              path: cookie.path || '/'
            });
          }
          
          // 重新加载页面以使Cookie生效
          await page.reload({ waitUntil: 'networkidle2' });
        }
        break;
    }
  }

  // 新增：智能检测页面结构
  async smartDetectPageStructure(url: string, loginConfig?: LoginConfig): Promise<SmartDetectionResult> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 处理登录
      if (loginConfig) {
        await this.handleLogin(page, url, loginConfig);
      } else {
        await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      }
      
      // 智能检测页面结构
      const detectionResult = await page.evaluate(() => {
        try {
          // 生成元素选择器的工具函数
          function getElementSelector(element: Element): string {
            if (!element) return '';
            
            // 优先使用ID
            if (element.id) {
              return '#' + element.id;
            }
            
            // 使用类名
            if (element.className && typeof element.className === 'string') {
              const classes = element.className.split(' ')
                .filter(cls => cls.trim() && !cls.match(/^(active|hover|focus|selected)$/))
                .slice(0, 2);
              if (classes.length > 0) {
                return '.' + classes.join('.');
              }
            }
            
            return element.tagName.toLowerCase();
          }
          
          // 检测表格
          const tables: any[] = [];
          const tableElements = document.querySelectorAll('table');
          
          tableElements.forEach((table, index) => {
            const rows = table.querySelectorAll('tr');
            if (rows.length >= 2) {
              const headerRow = rows[0];
              const dataRows = Array.from(rows).slice(1, Math.min(4, rows.length));
              const headerCells = headerRow.querySelectorAll('th, td');
              
              const columns: any[] = [];
              headerCells.forEach((cell, cellIndex) => {
                const columnName = cell.textContent?.trim() || `列${cellIndex + 1}`;
                const selector = `table:nth-of-type(${index + 1}) tr:not(:first-child) td:nth-child(${cellIndex + 1})`;
                
                // 检测列类型
                let type = 'text';
                const sampleCell = dataRows[0]?.querySelectorAll('td')[cellIndex];
                if (sampleCell) {
                  if (sampleCell.querySelector('a')) type = 'href';
                  else if (sampleCell.querySelector('img')) type = 'src';
                }
                
                columns.push({
                  name: columnName,
                  selector,
                  type
                });
              });
              
              // 生成样本数据
              const sampleData: any[] = [];
              dataRows.slice(0, 2).forEach((row) => {
                const cells = row.querySelectorAll('td');
                const rowData: any = {};
                
                columns.forEach((col, colIndex) => {
                  const cell = cells[colIndex];
                  if (cell) {
                    if (col.type === 'href') {
                      const link = cell.querySelector('a');
                      rowData[col.name] = link?.getAttribute('href') || cell.textContent?.trim();
                    } else if (col.type === 'src') {
                      const img = cell.querySelector('img');
                      rowData[col.name] = img?.getAttribute('src') || cell.textContent?.trim();
                    } else {
                      rowData[col.name] = cell.textContent?.trim();
                    }
                  }
                });
                
                if (Object.keys(rowData).length > 0) {
                  sampleData.push(rowData);
                }
              });
              
              if (columns.length > 0) {
                tables.push({
                  selector: `table:nth-of-type(${index + 1})`,
                  rowSelector: `table:nth-of-type(${index + 1}) tr:not(:first-child)`,
                  columns,
                  sampleData
                });
              }
            }
          });
          
          // 检测列表结构
          const lists: any[] = [];
          const listSelectors = [
            'ul > li', 'ol > li', '.list > .item', '.list-item', 
            '[class*="item"]', '[class*="card"]', '.product', '.news-item'
          ];
          
          listSelectors.forEach((selector) => {
            const items = document.querySelectorAll(selector);
            if (items.length >= 2) {
              const sampleItems = Array.from(items).slice(0, 3);
              const fields: any[] = [];
              
              // 分析第一个项目的结构
              const firstItem = sampleItems[0];
              
              // 检测标题
              const titleElements = firstItem.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .name, [class*="name"]');
              if (titleElements.length > 0) {
                const titleSelector = getElementSelector(titleElements[0]);
                fields.push({
                  name: '标题',
                  selector: `${selector} ${titleSelector}`,
                  type: 'text'
                });
              }
              
              // 检测链接
              const linkElements = firstItem.querySelectorAll('a');
              if (linkElements.length > 0) {
                const linkSelector = getElementSelector(linkElements[0]);
                fields.push({
                  name: '链接',
                  selector: `${selector} ${linkSelector}`,
                  type: 'href'
                });
              }
              
              // 检测图片
              const imgElements = firstItem.querySelectorAll('img');
              if (imgElements.length > 0) {
                const imgSelector = getElementSelector(imgElements[0]);
                fields.push({
                  name: '图片',
                  selector: `${selector} ${imgSelector}`,
                  type: 'src'
                });
              }
              
              // 生成样本数据
              const sampleData: any[] = [];
              sampleItems.forEach((item) => {
                const rowData: any = {};
                
                fields.forEach(field => {
                  try {
                    const relativePath = field.selector.replace(selector + ' ', '');
                    const element = item.querySelector(relativePath);
                    if (element) {
                      if (field.type === 'href') {
                        rowData[field.name] = element.getAttribute('href') || element.textContent?.trim();
                      } else if (field.type === 'src') {
                        rowData[field.name] = element.getAttribute('src') || element.textContent?.trim();
                      } else {
                        rowData[field.name] = element.textContent?.trim();
                      }
                    }
                  } catch (e) {
                    // 忽略错误
                  }
                });
                
                if (Object.keys(rowData).length > 0) {
                  sampleData.push(rowData);
                }
              });
              
              if (fields.length > 0) {
                lists.push({
                  containerSelector: selector.split(' > ')[0] || selector.split(' ')[0],
                  itemSelector: selector,
                  fields,
                  sampleData
                });
              }
            }
          });
          
          return {
            tables,
            lists
          };
        } catch (error) {
          console.error('检测错误:', error);
          return {
            tables: [],
            lists: []
          };
        }
      });
      
      return detectionResult;
    } catch (error) {
      console.error('smartDetectPageStructure错误:', error);
      throw error;
    } finally {
      await page.close();
    }
  }

  // 新增：根据坐标获取元素信息
  async getElementAtCoordinate(url: string, x: number, y: number, loginConfig?: LoginConfig): Promise<ElementSelection | null> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 设置更宽松的页面加载策略，处理反爬虫机制
      await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36');
      
      // 禁用图片和字体加载以加快速度
      await page.setRequestInterception(true);
      page.on('request', (req) => {
        const resourceType = req.resourceType();
        if (['image', 'font', 'media'].includes(resourceType)) {
          req.abort();
        } else {
          req.continue();
        }
      });
      
      // 处理登录
      if (loginConfig) {
        await this.handleLogin(page, url, loginConfig);
      } else {
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
      }
      
      // 等待页面稳定
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 检查页面是否正常加载
      const pageTitle = await page.title();
      if (!pageTitle || pageTitle.includes('Error') || pageTitle.includes('404')) {
        throw new Error('页面加载失败或返回错误页面');
      }
      
      // 验证坐标是否在页面范围内
      const viewport = await page.viewport();
      if (x < 0 || y < 0 || x > viewport!.width || y > viewport!.height) {
        throw new Error('坐标超出页面范围');
      }
      
      // 获取指定坐标的元素 - 增强错误处理
      // @ts-ignore - page.evaluate中的DOM API是正确的
      const elementInfo = await page.evaluate((x, y) => {
        try {
          const element = document.elementFromPoint(x, y);
          if (!element) {
            return null;
          }
          
          // 生成选择器
          const generateSelector = (el: any): string => {
            try {
              // 优先使用ID
              if (el.id && el.id.trim()) {
                return '#' + el.id;
              }
              
              // 使用类名
              if (el.className && typeof el.className === 'string') {
                const classes = el.className.split(' ')
                  .filter((cls: string) => cls.trim() && !cls.match(/^(active|hover|focus|selected)$/))
                  .slice(0, 2);
                if (classes.length > 0) {
                  return '.' + classes.join('.');
                }
              }
              
              // 使用属性选择器
              const attrs = ['data-id', 'data-testid', 'name', 'rel', 'role'];
              for (const attr of attrs) {
                const value = el.getAttribute(attr);
                if (value) {
                  return `[${attr}="${value}"]`;
                }
              }
              
              // 生成路径选择器
              const path = [];
              let current = el;
              
              while (current && current.nodeType === 1 && current !== document.body) {
                let selector = current.nodeName.toLowerCase();
                
                if (current.id && current.id.trim()) {
                  selector += '#' + current.id;
                  path.unshift(selector);
                  break;
                }
                
                const parent = current.parentNode as any;
                if (parent) {
                  const siblings = Array.from(parent.children).filter(
                    (child: any) => child.nodeName.toLowerCase() === selector.split('#')[0].split('.')[0]
                  );
                  if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += `:nth-of-type(${index})`;
                  }
                }
                
                path.unshift(selector);
                current = parent;
                
                if (path.length >= 5) break;
              }
              
              return path.join(' > ');
            } catch (error) {
              console.error('生成选择器时出错:', error);
              return el.tagName.toLowerCase();
            }
          };
          
          const rect = element.getBoundingClientRect();
          const tagName = element.tagName.toLowerCase();
          const text = element.textContent?.trim() || '';
          
          // 获取所有属性
          const attributes: Record<string, string> = {};
          try {
            for (let i = 0; i < element.attributes.length; i++) {
              const attr = element.attributes[i];
              attributes[attr.name] = attr.value;
            }
          } catch (error) {
            console.warn('获取元素属性时出错:', error);
          }
          
          // 推荐字段类型
          let suggestedType: 'text' | 'href' | 'src' | 'attribute' = 'text';
          let suggestedName = text.substring(0, 20) || '新字段';
          let suggestedAttribute: string | undefined;
          
          if (tagName === 'a') {
            suggestedType = 'href';
            suggestedName = '链接';
          } else if (tagName === 'img') {
            suggestedType = 'src';
            suggestedName = '图片';
          } else if (element.querySelector('a')) {
            suggestedType = 'href';
            suggestedName = '链接';
          } else if (element.querySelector('img')) {
            suggestedType = 'src';
            suggestedName = '图片';
          }
          
          return {
            selector: generateSelector(element),
            element: {
              tagName,
              text,
              attributes,
              rect: {
                x: rect.x,
                y: rect.y,
                width: rect.width,
                height: rect.height
              }
            },
            suggestedField: {
              name: suggestedName,
              type: suggestedType,
              attribute: suggestedAttribute
            }
          };
        } catch (error) {
          console.error('执行坐标检测时出错:', error);
          return null;
        }
      }, x, y);
      
      return elementInfo;
    } catch (error: any) {
      console.error('getElementAtCoordinate 错误:', error);
      
      // 提供更具体的错误信息
      if (error.message?.includes('timeout')) {
        throw new Error('页面加载超时，请检查网络连接或目标网站状态');
      } else if (error.message?.includes('navigation')) {
        throw new Error('页面导航失败，请检查URL是否正确');
      } else if (error.message?.includes('坐标超出页面范围')) {
        throw error;
      } else {
        throw new Error(`获取元素失败: ${error.message || '未知错误'}`);
      }
    } finally {
      await page.close();
    }
  }

  // 新增：执行页面交互
  async performPageInteraction(url: string, interactions: PageInteraction[], loginConfig?: LoginConfig): Promise<PageScreenshot> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 处理登录
      if (loginConfig) {
        await this.handleLogin(page, url, loginConfig);
      } else {
        await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      }
      
      // 执行交互操作
      for (const interaction of interactions) {
        switch (interaction.type) {
          case 'click':
            await page.click(interaction.selector!);
            break;
          case 'input':
            // @ts-ignore - value属性在input类型的交互中存在
            await page.type(interaction.selector!, interaction.value!);
            break;
          case 'scroll':
            if (interaction.scrollY !== undefined) {
              // @ts-ignore - page.evaluate中的window API是正确的
              await page.evaluate((y) => window.scrollTo(0, y), interaction.scrollY);
            }
            break;
          case 'wait':
            if (interaction.waitTime !== undefined) {
              await new Promise(resolve => setTimeout(resolve, interaction.waitTime! * 1000));
            }
            break;
        }
      }
      
      // 返回交互后的页面截图
      return await this.getPagePreview(page.url(), true);
    } finally {
      await page.close();
    }
  }

  async validateSelector(url: string, selector: string): Promise<{ valid: boolean; count: number; error?: string }> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      
      const elements = await page.$$(selector);
      return {
        valid: elements.length > 0,
        count: elements.length
      };
    } catch (error) {
      return {
        valid: false,
        count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      await page.close();
    }
  }

  async extractFieldData(page: Page, field: ScrapingField): Promise<string | null> {
    try {
      const element = await page.$(field.selector);
      if (!element) return null;

      switch (field.type) {
        case 'text':
          return await element.evaluate((el) => el.textContent?.trim() || '');
        
        case 'href':
          return await element.evaluate((el) => 
            el.getAttribute('href') || (el as any).href || ''
          );
        
        case 'src':
          return await element.evaluate((el) => 
            el.getAttribute('src') || (el as any).src || ''
          );
        
        case 'attribute':
          if (!field.attribute) return null;
          return await element.evaluate((el, attr) => 
            el.getAttribute(attr) || '', field.attribute
          );
        
        default:
          return null;
      }
    } catch (error) {
      console.error(`提取字段 ${field.fieldName} 失败:`, error);
      return null;
    }
  }

  async scrapeTask(taskId: string): Promise<ScrapingResult> {
    const task = await this.storage.getTask(taskId);
    if (!task) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    await this.storage.updateTask(taskId, { status: 'running' });
    await this.initBrowser();
    
    const page = await this.browser!.newPage();
    const scrapedData: Record<string, any>[] = [];
    let currentUrl = task.url;
    let pageNumber = 1;
    const maxPages = 10;

    try {
      await page.setViewport({ width: 1920, height: 1080 });
      
      while (currentUrl && pageNumber <= maxPages) {
        console.log(`正在抓取第 ${pageNumber} 页: ${currentUrl}`);
        
        // 处理登录
        if (task.loginConfig) {
          await this.handleLogin(page, currentUrl, task.loginConfig);
        } else {
          await page.goto(currentUrl, { waitUntil: 'networkidle2', timeout: 30000 });
        }
        
        // 执行预操作
        if (task.preActions && task.preActions.length > 0) {
          for (const action of task.preActions) {
            switch (action.type) {
              case 'click':
                if (action.selector) {
                  await page.click(action.selector);
                }
                break;
              case 'scroll':
                if (action.scrollY !== undefined) {
                  // @ts-ignore - page.evaluate中的window API是正确的
                  await page.evaluate((y) => window.scrollTo(0, y), action.scrollY);
                }
                break;
              case 'wait':
                if (action.waitTime !== undefined) {
                  await new Promise(resolve => setTimeout(resolve, action.waitTime! * 1000));
                }
                break;
            }
          }
        }

        await new Promise(resolve => setTimeout(resolve, 2000));

        // 提取数据
        const pageData = await this.extractPageData(page, task.fields);
        scrapedData.push(...pageData);

        // 检查是否有下一页
        if (task.nextPageSelector) {
          try {
            const nextPageElement = await page.$(task.nextPageSelector);
            if (nextPageElement) {
              const nextPageHref = await nextPageElement.evaluate((el) => 
                el.getAttribute('href') || (el as any).href
              );
              
              if (nextPageHref) {
                const nextPageUrl = new URL(nextPageHref, currentUrl).href;
                
                if (nextPageUrl && nextPageUrl !== currentUrl) {
                  currentUrl = nextPageUrl;
                  pageNumber++;
                } else {
                  break;
                }
              } else {
                break;
              }
            } else {
              break;
            }
          } catch (error) {
            console.log('没有找到下一页，结束抓取');
            break;
          }
        } else {
          break;
        }
      }

      const result: ScrapingResult = {
        taskId,
        data: scrapedData,
        totalCount: scrapedData.length,
        scrapedAt: new Date().toISOString()
      };

      await this.storage.saveResult(result);
      await this.storage.updateTask(taskId, { status: 'success' });
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.storage.updateTask(taskId, { 
        status: 'failed', 
        error: errorMessage 
      });
      throw error;
    } finally {
      await page.close();
    }
  }

  private async extractPageData(page: Page, fields: ScrapingField[]): Promise<Record<string, any>[]> {
    let maxCount = 0;
    let mainField: ScrapingField | null = null;

    for (const field of fields) {
      const elements = await page.$$(field.selector);
      if (elements.length > maxCount) {
        maxCount = elements.length;
        mainField = field;
      }
    }

    if (maxCount === 0) {
      return [];
    }

    const results: Record<string, any>[] = [];

    for (let i = 0; i < maxCount; i++) {
      const rowData: Record<string, any> = {};

      for (const field of fields) {
        try {
          const elements = await page.$$(field.selector);
          if (elements[i]) {
            const value = await this.extractFieldData(page, {
              ...field,
              selector: `(${field.selector})[${i + 1}]`
            });
            rowData[field.fieldName] = value;
          } else {
            rowData[field.fieldName] = null;
          }
        } catch (error) {
          rowData[field.fieldName] = null;
        }
      }

      results.push(rowData);
    }

    return results;
  }

  // 保留原有的脚本注入功能（兼容性）
  async injectSelectorScript(url: string): Promise<string> {
    await this.initBrowser();
    const page = await this.browser!.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      return await page.content();
    } finally {
      await page.close();
    }
  }
} 